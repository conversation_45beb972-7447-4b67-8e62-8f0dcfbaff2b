export interface ClickstreamEvent {
  id: string;
  userId: string;
  sessionId: string;
  timestamp: number;
  eventType: ClickstreamEventType;
  elementId?: string;
  elementType?: string;
  value?: any;
  metadata?: Record<string, any>;
}

export enum ClickstreamEventType {
  // Form events
  FORM_OPENED = 'form_opened',
  FORM_CLOSED = 'form_closed',
  FORM_SUBMITTED = 'form_submitted',
  FORM_CANCELLED = 'form_cancelled',

  // Field events
  FIELD_FOCUSED = 'field_focused',
  FIELD_BLURRED = 'field_blurred',
  FIELD_CHANGED = 'field_changed',

  // Mouse events
  MOUSE_CLICK = 'mouse_click',
  MOUSE_RIGHT_CLICK = 'mouse_right_click',
  MOUSE_WHEEL_CLICK = 'mouse_wheel_click',
  MOUSE_MOVE = 'mouse_move',
  MOUSE_HOVER = 'mouse_hover',

  // Navigation events
  PAGE_LOADED = 'page_loaded',
  PAGE_UNLOADED = 'page_unloaded',

  // Annotation events
  ANNOTATION_STARTED = 'annotation_started',
  ANNOTATION_COMPLETED = 'annotation_completed',
  ANNOTATION_DELETED = 'annotation_deleted',

  // Rectangle drawing events
  RECTANGLE_DRAW_START = 'rectangle_draw_start',
  RECTANGLE_DRAW_END = 'rectangle_draw_end',
  RECTANGLE_SELECTED = 'rectangle_selected',

  // AI interaction events
  AI_HELPER_ACTIVATED = 'ai_helper_activated',
  AI_HELPER_DEACTIVATED = 'ai_helper_deactivated',
  AI_RECTANGLE_CLICKED = 'ai_rectangle_clicked',
  AI_SUGGESTION_SHOWN = 'ai_suggestion_shown',
  AI_SUGGESTION_ACCEPTED = 'ai_suggestion_accepted',
  AI_SUGGESTION_MODIFIED = 'ai_suggestion_modified',
  AI_SUGGESTION_REJECTED= 'ai_suggestion_rejected'

}

export interface ReflectionTimeData {
  annotationId: string;
  userId: string;
  sessionId: string;
  spectroName: string;

  // Time measurements
  formOpenedAt: number;
  formClosedAt: number;
  totalReflectionTime: number; // in milliseconds

  // Field interaction times
  bruiteurFieldTime: number; // time spent on noise type field
  confidenceFieldTime: number; // time spent on confidence field

  // Interaction counts
  bruiteurChanges: number;
  confidenceChanges: number;

  // AI suggestion interactions
  aiSuggestionsShown: number;
  aiSuggestionsAccepted: number;
  aiSuggestionsRejected: number;
  aiSuggestionsModified: number;

  // Final values
  finalBruiteur: string;
  finalConfidence: number;
  wasAiSuggestionAccepted: boolean;

  // Additional metadata
  wasSubmitted: boolean;
  wasCancelled: boolean;
  wasDeleted: boolean;

  // Context data
  rectangleData?: {
    startX: number;
    startY: number;
    width: number;
    height: number;
    fLeft?: number;
    fRight?: number;
    tTop?: number;
    tBottom?: number;
  };
}

export interface FieldInteraction {
  fieldName: string;
  startTime: number;
  endTime: number;
  duration: number;
  initialValue: any;
  finalValue: any;
  changeCount: number;
  focusCount: number;
}

export interface AnnotationSession {
  sessionId: string;
  userId: string;
  spectroName: string;
  startTime: number;
  endTime?: number;
  totalDuration?: number;
  annotationCount: number;
  reflectionTimes: ReflectionTimeData[];
  clickstreamEvents: ClickstreamEvent[];
}

export interface ClickstreamAnalytics {
  userId: string;
  spectroName: string;
  totalAnnotations: number;
  averageReflectionTime: number;
  medianReflectionTime: number;
  minReflectionTime: number;
  maxReflectionTime: number;
  totalSessionTime: number;

  // Field-specific analytics
  averageBruiteurTime: number;
  averageConfidenceTime: number;

  // Interaction patterns
  averageChangesPerAnnotation: number;
  mostCommonBruiteur: string;
  mostCommonConfidence: number;

  // Efficiency metrics
  annotationsPerMinute: number;
  timeToFirstInteraction: number;
  hesitationScore: number; // based on changes and time spent

  // Click tracking metrics
  totalClicks: number;
  totalLeftClicks: number;
  totalRightClicks: number;
  totalWheelClicks: number;
  clicksPerMinute: number;
  clicksPerAnnotation: number;

  //Ai interactions metrics
  aiHelperUsageCount: number;
  aiRectangleClickCount: number;
  aiSuggestionsAccepted: number;
  aiSuggestionsModified: number;
  aiSuggestionsRejected: number;
  averageAIReflectionTime: number;
  aiAcceptanceRate: number;
  aiModificationRate: number;
  aiRejectionRate: number;
  totalAIInteractions: number;

}

export interface ClickstreamConfig {
  enableTracking: boolean;
  trackMouseMovements: boolean;
  mouseMoveThrottleMs: number;
  batchSize: number;
  flushIntervalMs: number;
  enableLocalStorage: boolean;
  apiEndpoint: string;
}
